<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP 服务器能力展示</title>
    
    <!-- 外部CSS资源 -->
    <link href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-100-M/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- 自定义样式 -->
    <link href="/static/style.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Noto Sans SC', sans-serif;
        }
        .title-font {
            font-family: 'Noto Serif SC', serif;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 头部 -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-6xl mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <i class="fas fa-robot text-3xl text-blue-600"></i>
                    <h1 class="text-2xl font-bold text-gray-800 title-font">MCP 服务器能力展示</h1>
                </div>
                <div class="flex items-center space-x-2 text-sm text-gray-600">
                    <i class="fas fa-circle text-green-500"></i>
                    <span id="connection-status">已连接</span>
                </div>
            </div>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="max-w-6xl mx-auto px-4 py-6">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
            
            <!-- 侧边栏 - 功能介绍 -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                        <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                        功能介绍
                    </h3>
                    <div class="space-y-3 text-sm text-gray-600">
                        <div class="flex items-start space-x-2">
                            <i class="fas fa-check-circle text-green-500 mt-1"></i>
                            <span>智能对话交互</span>
                        </div>
                        <div class="flex items-start space-x-2">
                            <i class="fas fa-check-circle text-green-500 mt-1"></i>
                            <span>MCP工具调用</span>
                        </div>
                        <div class="flex items-start space-x-2">
                            <i class="fas fa-check-circle text-green-500 mt-1"></i>
                            <span>实时交互展示</span>
                        </div>
                        <div class="flex items-start space-x-2">
                            <i class="fas fa-check-circle text-green-500 mt-1"></i>
                            <span>CAN信号查询</span>
                        </div>
                    </div>
                </div>

                <!-- 示例问题 -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                        <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>
                        示例问题
                    </h3>
                    <div class="space-y-2">
                        <button class="example-question w-full text-left p-3 bg-gray-50 hover:bg-gray-100 rounded-lg text-sm transition-colors">
                            查看可用的项目列表
                        </button>
                        <button class="example-question w-full text-left p-3 bg-gray-50 hover:bg-gray-100 rounded-lg text-sm transition-colors">
                            搜索包含"速度"的CAN信号
                        </button>
                        <button class="example-question w-full text-left p-3 bg-gray-50 hover:bg-gray-100 rounded-lg text-sm transition-colors">
                            获取项目统计信息
                        </button>
                    </div>
                </div>
            </div>

            <!-- 对话区域 -->
            <div class="lg:col-span-3">
                <div class="bg-white rounded-lg shadow-sm">
                    <!-- 对话头部 -->
                    <div class="border-b px-6 py-4">
                        <div class="flex items-center justify-between">
                            <h2 class="text-xl font-semibold text-gray-800 flex items-center">
                                <i class="fas fa-comments text-blue-500 mr-2"></i>
                                智能对话
                            </h2>
                            <button id="clear-chat" class="text-gray-500 hover:text-red-500 transition-colors">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 消息区域 -->
                    <div id="chat-messages" class="chat-container px-6 py-4 space-y-4 scroll-smooth">
                        <!-- 欢迎消息 -->
                        <div class="message-bubble flex items-start space-x-3">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-robot text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="flex-1">
                                <div class="bg-blue-50 rounded-lg p-4">
                                    <p class="text-gray-800">
                                        欢迎使用MCP服务器能力展示系统！我可以帮您查询CAN信号信息、项目统计等。请输入您的问题，我会调用相应的MCP工具来为您提供准确的信息。
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 输入区域 -->
                    <div class="border-t px-6 py-4">
                        <div class="flex space-x-4">
                            <div class="flex-1">
                                <textarea 
                                    id="message-input" 
                                    placeholder="请输入您的问题..." 
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                                    rows="2"
                                ></textarea>
                            </div>
                            <div class="flex flex-col space-y-2">
                                <button 
                                    id="send-button" 
                                    class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors flex items-center justify-center"
                                >
                                    <i class="fas fa-paper-plane mr-2"></i>
                                    发送
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="/static/app.js"></script>
</body>
</html>
