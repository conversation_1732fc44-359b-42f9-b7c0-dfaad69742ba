#!/usr/bin/env python3
"""
MCP服务器能力展示应用启动脚本
"""

import uvicorn
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

if __name__ == "__main__":
    port = int(os.getenv("PORT", 8000))
    
    print("=" * 60)
    print("🤖 MCP服务器能力展示系统")
    print("=" * 60)
    print(f"🌐 服务地址: http://localhost:{port}")
    print(f"🔑 DeepSeek API: {'已配置' if os.getenv('DEEPSEEK_API_KEY') else '未配置'}")
    print(f"🔧 MCP服务器: {os.getenv('MCP_SERVER_URL', '未配置')}")
    print("=" * 60)
    print("正在启动服务器...")
    
    uvicorn.run(
        "app:app",
        host="0.0.0.0",
        port=port,
        reload=True,
        log_level="info"
    )
