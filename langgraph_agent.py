"""
LangGraph Agent集成
使用LangGraph构建AI Agent，整合DeepSeek API和MCP工具
"""

import json
import asyncio
from typing import Dict, Any, List, AsyncGenerator, Optional
import httpx
import logging
from mcp_tools import MCPClient

logger = logging.getLogger(__name__)

class DeepSeekLLM:
    """DeepSeek LLM客户端"""
    
    def __init__(self, api_key: str, base_url: str = "https://api.deepseek.com"):
        self.api_key = api_key
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=60.0)
    
    async def chat_completion(self, messages: List[Dict[str, Any]], tools: List[Dict[str, Any]] = None, stream: bool = False):
        """聊天完成API调用"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": "deepseek-chat",
            "messages": messages,
            "stream": stream,
            "temperature": 0.7,
            "max_tokens": 2000
        }
        
        if tools:
            payload["tools"] = tools
            payload["tool_choice"] = "auto"
        
        if stream:
            return self._stream_response(headers, payload)
        else:
            response = await self.client.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=payload
            )
            return response.json()
    
    async def _stream_response(self, headers: Dict[str, str], payload: Dict[str, Any]):
        """处理流式响应"""
        async with self.client.stream(
            "POST",
            f"{self.base_url}/chat/completions",
            headers=headers,
            json=payload
        ) as response:
            async for line in response.aiter_lines():
                if line.startswith("data: "):
                    data = line[6:]
                    if data.strip() == "[DONE]":
                        break
                    try:
                        chunk = json.loads(data)
                        yield chunk
                    except json.JSONDecodeError:
                        continue

class MCPAgent:
    """MCP Agent - 整合LLM和MCP工具的智能代理"""
    
    def __init__(self, llm: DeepSeekLLM, mcp_client: MCPClient):
        self.llm = llm
        self.mcp_client = mcp_client
        self.conversation_history: List[Dict[str, Any]] = []
        
        # 系统提示
        self.system_prompt = """你是一个专业的CAN信号查询助手，可以帮助用户查询和分析CAN信号信息。

你有以下MCP工具可以使用：
1. get_available_projects - 获取可用项目列表
2. switch_project - 切换当前活动项目  
3. get_signal_exact - 精确查询CAN信号信息
4. search_signals_fuzzy - 模糊查询CAN信号信息
5. get_message_info - 查询CAN消息信息
6. search_messages_fuzzy - 模糊查询CAN消息信息
7. get_signals_by_message - 根据消息名称获取信号
8. get_project_statistics - 获取项目统计信息

使用指南：
- 当用户询问项目相关信息时，使用项目管理工具
- 当用户询问具体信号时，优先使用精确查询，如果没有结果再使用模糊查询
- 当用户询问消息相关信息时，使用消息查询工具
- 始终提供清晰、结构化的回答
- 如果工具调用失败，请解释原因并建议替代方案

请根据用户的问题选择合适的工具，并提供专业、准确的回答。"""
    
    async def initialize(self):
        """初始化Agent"""
        await self.mcp_client.initialize()
        logger.info("MCP Agent初始化完成")
    
    async def process_message(self, user_message: str) -> AsyncGenerator[Dict[str, Any], None]:
        """处理用户消息并生成流式响应"""
        try:
            # 添加用户消息到历史
            self.conversation_history.append({"role": "user", "content": user_message})
            
            # 构建消息列表
            messages = [{"role": "system", "content": self.system_prompt}] + self.conversation_history
            
            # 获取可用工具
            tools = self.mcp_client.get_tools_for_openai()
            
            yield {"type": "message_start"}
            
            # 调用LLM
            full_response = ""
            assistant_message = {"role": "assistant", "content": ""}
            
            async for chunk in self.llm.chat_completion(messages, tools, stream=True):
                if "choices" in chunk and len(chunk["choices"]) > 0:
                    choice = chunk["choices"][0]
                    delta = choice.get("delta", {})
                    
                    # 处理文本内容
                    if "content" in delta and delta["content"]:
                        content = delta["content"]
                        full_response += content
                        assistant_message["content"] = full_response
                        yield {"type": "message_chunk", "content": full_response}
                    
                    # 处理工具调用
                    if "tool_calls" in delta:
                        for tool_call in delta["tool_calls"]:
                            if tool_call.get("function"):
                                func = tool_call["function"]
                                tool_name = func.get("name")
                                arguments_str = func.get("arguments", "{}")
                                
                                try:
                                    arguments = json.loads(arguments_str)
                                    
                                    # 发送工具调用信息
                                    yield {
                                        "type": "tool_call",
                                        "tool_name": tool_name,
                                        "arguments": arguments
                                    }
                                    
                                    # 调用MCP工具
                                    result = await self.mcp_client.call_tool(tool_name, arguments)
                                    
                                    # 发送工具结果
                                    yield {
                                        "type": "tool_result",
                                        "tool_name": tool_name,
                                        "result": result
                                    }
                                    
                                    # 将工具调用和结果添加到消息历史
                                    tool_call_message = {
                                        "role": "assistant",
                                        "content": None,
                                        "tool_calls": [{
                                            "id": f"call_{tool_name}",
                                            "type": "function",
                                            "function": {
                                                "name": tool_name,
                                                "arguments": arguments_str
                                            }
                                        }]
                                    }
                                    
                                    tool_result_message = {
                                        "role": "tool",
                                        "tool_call_id": f"call_{tool_name}",
                                        "content": json.dumps(result, ensure_ascii=False)
                                    }
                                    
                                    # 更新消息历史
                                    messages.append(tool_call_message)
                                    messages.append(tool_result_message)
                                    
                                    # 继续对话以获取基于工具结果的回复
                                    follow_response = ""
                                    async for follow_chunk in self.llm.chat_completion(messages, tools, stream=True):
                                        if "choices" in follow_chunk and len(follow_chunk["choices"]) > 0:
                                            follow_choice = follow_chunk["choices"][0]
                                            follow_delta = follow_choice.get("delta", {})
                                            
                                            if "content" in follow_delta and follow_delta["content"]:
                                                content = follow_delta["content"]
                                                follow_response += content
                                                yield {"type": "message_chunk", "content": follow_response}
                                    
                                    # 更新助手消息内容
                                    assistant_message["content"] = follow_response
                                    
                                except json.JSONDecodeError as e:
                                    logger.error(f"工具参数解析失败: {e}")
                                    yield {"type": "error", "message": "工具参数解析失败"}
                                except Exception as e:
                                    logger.error(f"工具调用失败: {e}")
                                    yield {"type": "error", "message": f"工具调用失败: {str(e)}"}
            
            # 添加助手回复到历史
            if assistant_message["content"]:
                self.conversation_history.append(assistant_message)
            
            yield {"type": "message_end"}
            
        except Exception as e:
            logger.error(f"处理消息时发生错误: {e}")
            yield {"type": "error", "message": f"处理消息时发生错误: {str(e)}"}
    
    def clear_history(self):
        """清空对话历史"""
        self.conversation_history = []
        logger.info("对话历史已清空")
    
    def get_conversation_summary(self) -> Dict[str, Any]:
        """获取对话摘要"""
        return {
            "message_count": len(self.conversation_history),
            "last_message": self.conversation_history[-1] if self.conversation_history else None
        }
