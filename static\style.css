/* 自定义样式 */
.chat-container {
    max-height: 70vh;
    overflow-y: auto;
}

.message-bubble {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.typing-indicator {
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

.tool-call-box {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    padding: 12px;
    margin: 8px 0;
    color: white;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
}

.tool-result-box {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    border-radius: 8px;
    padding: 12px;
    margin: 8px 0;
    color: white;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
}

.scroll-smooth {
    scroll-behavior: smooth;
}

/* 滚动条样式 */
.chat-container::-webkit-scrollbar {
    width: 6px;
}

.chat-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.chat-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.chat-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
