from fastapi import FastAP<PERSON>, Request
from fastapi.responses import HTMLResponse, StreamingResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
import json
import asyncio
import os
from dotenv import load_dotenv
from typing import AsyncGenerator, Dict, Any, List
import httpx
from pydantic import BaseModel
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 加载环境变量
load_dotenv()

app = FastAPI(title="MCP Server Demo")

# 静态文件和模板
app.mount("/static", StaticFiles(directory="static"), name="static")
templates = Jinja2Templates(directory="templates")

# 配置
DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY")
DEEPSEEK_API_BASE = os.getenv("DEEPSEEK_API_BASE", "https://api.deepseek.com")
MCP_SERVER_URL = os.getenv("MCP_SERVER_URL")

class ChatMessage(BaseModel):
    message: str

from mcp_tools import create_mcp_client_with_fallback
from langgraph_agent import DeepSeekLLM, MCPAgent

# 全局实例
mcp_client = None
agent = None

async def initialize_agent():
    """初始化Agent"""
    global mcp_client, agent

    if not mcp_client and MCP_SERVER_URL:
        mcp_client = create_mcp_client_with_fallback(MCP_SERVER_URL)
        await mcp_client.initialize()
        logger.info("MCP客户端初始化完成")

    if not agent and DEEPSEEK_API_KEY and mcp_client:
        llm = DeepSeekLLM(DEEPSEEK_API_KEY, DEEPSEEK_API_BASE)
        agent = MCPAgent(llm, mcp_client)
        await agent.initialize()
        logger.info("MCP Agent初始化完成")

@app.on_event("startup")
async def startup_event():
    """应用启动时初始化Agent"""
    try:
        await initialize_agent()
        logger.info("应用启动完成")
    except Exception as e:
        logger.error(f"应用启动失败: {e}")
        # 不抛出异常，让应用继续运行

@app.get("/", response_class=HTMLResponse)
async def index(request: Request):
    return templates.TemplateResponse("index.html", {"request": request})

@app.post("/api/chat")
async def chat_endpoint(message: ChatMessage):
    """聊天API端点"""

    async def generate_response() -> AsyncGenerator[str, None]:
        try:
            if not agent:
                yield f"data: {json.dumps({'type': 'error', 'message': 'Agent未初始化，请检查配置'})}\n\n"
                return

            # 使用Agent处理消息
            async for response_data in agent.process_message(message.message):
                yield f"data: {json.dumps(response_data, ensure_ascii=False)}\n\n"

        except Exception as e:
            logger.error(f"聊天端点错误: {e}")
            yield f"data: {json.dumps({'type': 'error', 'message': f'处理请求时发生错误: {str(e)}'})}\n\n"

    return StreamingResponse(
        generate_response(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream"
        }
    )

@app.post("/api/clear")
async def clear_chat():
    """清空聊天历史"""
    if agent:
        agent.clear_history()
        return {"success": True, "message": "聊天历史已清空"}
    return {"success": False, "message": "Agent未初始化"}

@app.get("/api/status")
async def get_status():
    """获取系统状态"""
    return {
        "mcp_client_initialized": mcp_client is not None,
        "agent_initialized": agent is not None,
        "mcp_server_url": MCP_SERVER_URL,
        "deepseek_configured": DEEPSEEK_API_KEY is not None
    }

if __name__ == "__main__":
    import uvicorn
    port = int(os.getenv("PORT", 8000))
    uvicorn.run(app, host="0.0.0.0", port=port)
