class ChatApp {
    constructor() {
        this.messagesContainer = document.getElementById('chat-messages');
        this.messageInput = document.getElementById('message-input');
        this.sendButton = document.getElementById('send-button');
        this.clearButton = document.getElementById('clear-chat');
        this.connectionStatus = document.getElementById('connection-status');
        
        this.isConnected = false;
        this.isProcessing = false;
        
        this.initializeEventListeners();
        this.connectToServer();
    }

    initializeEventListeners() {
        // 发送按钮点击事件
        this.sendButton.addEventListener('click', () => this.sendMessage());
        
        // 输入框回车事件
        this.messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });
        
        // 清空聊天记录
        this.clearButton.addEventListener('click', () => this.clearChat());
        
        // 示例问题点击事件
        document.querySelectorAll('.example-question').forEach(button => {
            button.addEventListener('click', () => {
                this.messageInput.value = button.textContent.trim();
                this.sendMessage();
            });
        });
    }

    connectToServer() {
        // 这里可以添加WebSocket连接逻辑
        this.updateConnectionStatus(true);
    }

    updateConnectionStatus(connected) {
        this.isConnected = connected;
        this.connectionStatus.textContent = connected ? '已连接' : '连接中断';
        this.connectionStatus.previousElementSibling.className = 
            `fas fa-circle ${connected ? 'text-green-500' : 'text-red-500'}`;
    }

    async sendMessage() {
        const message = this.messageInput.value.trim();
        if (!message || this.isProcessing) return;

        this.isProcessing = true;
        this.updateSendButton(true);
        
        // 添加用户消息
        this.addMessage(message, 'user');
        this.messageInput.value = '';
        
        // 添加加载指示器
        const loadingId = this.addLoadingMessage();
        
        try {
            // 发送请求到后端
            const response = await fetch('/api/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ message: message })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            // 移除加载指示器
            this.removeLoadingMessage(loadingId);
            
            // 处理流式响应
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let assistantMessageId = null;
            
            while (true) {
                const { done, value } = await reader.read();
                if (done) break;
                
                const chunk = decoder.decode(value);
                const lines = chunk.split('\n');
                
                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        try {
                            const data = JSON.parse(line.slice(6));
                            
                            if (data.type === 'message_start') {
                                assistantMessageId = this.addMessage('', 'assistant');
                            } else if (data.type === 'message_chunk' && assistantMessageId) {
                                this.updateMessage(assistantMessageId, data.content);
                            } else if (data.type === 'tool_call') {
                                this.addToolCallMessage(data.tool_name, data.arguments);
                            } else if (data.type === 'tool_result') {
                                this.addToolResultMessage(data.tool_name, data.result);
                            } else if (data.type === 'error') {
                                this.addMessage(`错误: ${data.message}`, 'error');
                            }
                        } catch (e) {
                            console.error('解析SSE数据失败:', e);
                        }
                    }
                }
            }
            
        } catch (error) {
            console.error('发送消息失败:', error);
            this.removeLoadingMessage(loadingId);
            this.addMessage('抱歉，发生了错误，请稍后重试。', 'error');
        } finally {
            this.isProcessing = false;
            this.updateSendButton(false);
        }
    }

    addMessage(content, type) {
        const messageId = 'msg-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
        const messageDiv = document.createElement('div');
        messageDiv.id = messageId;
        messageDiv.className = 'message-bubble flex items-start space-x-3';
        
        let iconClass, bgClass, textClass;
        
        switch (type) {
            case 'user':
                iconClass = 'fas fa-user';
                bgClass = 'bg-blue-500';
                textClass = 'bg-blue-100';
                break;
            case 'assistant':
                iconClass = 'fas fa-robot';
                bgClass = 'bg-green-500';
                textClass = 'bg-green-50';
                break;
            case 'error':
                iconClass = 'fas fa-exclamation-triangle';
                bgClass = 'bg-red-500';
                textClass = 'bg-red-50';
                break;
            default:
                iconClass = 'fas fa-info-circle';
                bgClass = 'bg-gray-500';
                textClass = 'bg-gray-50';
        }
        
        messageDiv.innerHTML = `
            <div class="flex-shrink-0">
                <div class="w-8 h-8 ${bgClass} rounded-full flex items-center justify-center">
                    <i class="${iconClass} text-white text-sm"></i>
                </div>
            </div>
            <div class="flex-1">
                <div class="${textClass} rounded-lg p-4">
                    <p class="text-gray-800 message-content">${this.formatMessage(content)}</p>
                </div>
            </div>
        `;
        
        this.messagesContainer.appendChild(messageDiv);
        this.scrollToBottom();
        
        return messageId;
    }

    updateMessage(messageId, newContent) {
        const messageElement = document.getElementById(messageId);
        if (messageElement) {
            const contentElement = messageElement.querySelector('.message-content');
            if (contentElement) {
                contentElement.innerHTML = this.formatMessage(newContent);
            }
        }
    }

    addLoadingMessage() {
        const loadingId = 'loading-' + Date.now();
        const loadingDiv = document.createElement('div');
        loadingDiv.id = loadingId;
        loadingDiv.className = 'message-bubble flex items-start space-x-3';
        
        loadingDiv.innerHTML = `
            <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-gray-500 rounded-full flex items-center justify-center">
                    <i class="fas fa-robot text-white text-sm"></i>
                </div>
            </div>
            <div class="flex-1">
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="typing-indicator flex items-center space-x-1">
                        <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                        <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                        <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                        <span class="ml-2 text-gray-600 text-sm">AI正在思考...</span>
                    </div>
                </div>
            </div>
        `;
        
        this.messagesContainer.appendChild(loadingDiv);
        this.scrollToBottom();
        
        return loadingId;
    }

    removeLoadingMessage(loadingId) {
        const loadingElement = document.getElementById(loadingId);
        if (loadingElement) {
            loadingElement.remove();
        }
    }

    addToolCallMessage(toolName, arguments) {
        const toolDiv = document.createElement('div');
        toolDiv.className = 'tool-call-box';
        toolDiv.innerHTML = `
            <div class="flex items-center mb-2">
                <i class="fas fa-cog mr-2"></i>
                <strong>调用工具: ${toolName}</strong>
            </div>
            <pre class="text-sm opacity-90">${JSON.stringify(arguments, null, 2)}</pre>
        `;
        
        this.messagesContainer.appendChild(toolDiv);
        this.scrollToBottom();
    }

    addToolResultMessage(toolName, result) {
        const resultDiv = document.createElement('div');
        resultDiv.className = 'tool-result-box';
        resultDiv.innerHTML = `
            <div class="flex items-center mb-2">
                <i class="fas fa-check-circle mr-2"></i>
                <strong>工具结果: ${toolName}</strong>
            </div>
            <pre class="text-sm opacity-90">${JSON.stringify(result, null, 2)}</pre>
        `;
        
        this.messagesContainer.appendChild(resultDiv);
        this.scrollToBottom();
    }

    formatMessage(content) {
        // 简单的文本格式化
        return content
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>');
    }

    updateSendButton(isLoading) {
        if (isLoading) {
            this.sendButton.disabled = true;
            this.sendButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>处理中...';
        } else {
            this.sendButton.disabled = false;
            this.sendButton.innerHTML = '<i class="fas fa-paper-plane mr-2"></i>发送';
        }
    }

    async clearChat() {
        try {
            // 调用后端清空API
            const response = await fetch('/api/clear', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            if (response.ok) {
                // 保留欢迎消息，清除其他消息
                const messages = this.messagesContainer.querySelectorAll('.message-bubble:not(:first-child), .tool-call-box, .tool-result-box');
                messages.forEach(message => message.remove());
            } else {
                console.error('清空聊天记录失败');
            }
        } catch (error) {
            console.error('清空聊天记录时发生错误:', error);
        }
    }

    scrollToBottom() {
        this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new ChatApp();
});
