"""
MCP工具集成模块
提供与远程MCP服务器的交互功能
"""

import httpx
import json
import asyncio
from typing import Dict, Any, List, Optional
import logging

logger = logging.getLogger(__name__)

class MCPTool:
    """MCP工具定义"""
    def __init__(self, name: str, description: str, input_schema: Dict[str, Any]):
        self.name = name
        self.description = description
        self.input_schema = input_schema
    
    def to_openai_format(self) -> Dict[str, Any]:
        """转换为OpenAI工具格式"""
        return {
            "type": "function",
            "function": {
                "name": self.name,
                "description": self.description,
                "parameters": self.input_schema
            }
        }

class MCPClient:
    """MCP客户端"""
    
    def __init__(self, server_url: str):
        self.server_url = server_url
        self.client = httpx.AsyncClient(timeout=30.0)
        self.tools: List[MCPTool] = []
        self._initialized = False
    
    async def initialize(self):
        """初始化客户端，获取可用工具"""
        if self._initialized:
            return
        
        try:
            # 获取工具列表
            tools_data = await self._call_mcp_method("tools/list")
            if tools_data and "tools" in tools_data:
                self.tools = []
                for tool_data in tools_data["tools"]:
                    tool = MCPTool(
                        name=tool_data.get("name", ""),
                        description=tool_data.get("description", ""),
                        input_schema=tool_data.get("inputSchema", {})
                    )
                    self.tools.append(tool)
                
                logger.info(f"成功加载 {len(self.tools)} 个MCP工具")
                self._initialized = True
            else:
                logger.warning("未能获取MCP工具列表")
                
        except Exception as e:
            logger.error(f"初始化MCP客户端失败: {e}")
    
    async def _call_mcp_method(self, method: str, params: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """调用MCP方法"""
        try:
            request_data = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": method
            }

            if params:
                request_data["params"] = params

            # 尝试GET请求用于SSE连接
            if method == "tools/list":
                try:
                    response = await self.client.get(
                        self.server_url,
                        headers={"Accept": "text/event-stream"}
                    )
                    if response.status_code == 200:
                        # 处理SSE响应
                        content = response.text
                        # 简单解析，实际应该处理SSE格式
                        logger.info(f"SSE响应: {content[:200]}...")
                        return {"tools": []}  # 暂时返回空工具列表
                except Exception as e:
                    logger.warning(f"SSE连接失败，尝试POST: {e}")

            # 回退到POST请求
            response = await self.client.post(
                self.server_url,
                json=request_data,
                headers={"Content-Type": "application/json"}
            )

            if response.status_code == 200:
                result = response.json()
                if "error" in result:
                    logger.error(f"MCP方法调用错误: {result['error']}")
                    return {"error": result["error"]}
                return result.get("result")
            else:
                logger.error(f"MCP调用HTTP错误: {response.status_code}")
                return {"error": f"HTTP {response.status_code}"}

        except Exception as e:
            logger.error(f"MCP方法调用异常: {e}")
            return {"error": str(e)}
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """调用指定的MCP工具"""
        if not self._initialized:
            await self.initialize()
        
        # 验证工具是否存在
        tool_exists = any(tool.name == tool_name for tool in self.tools)
        if not tool_exists:
            return {"error": f"工具 '{tool_name}' 不存在"}
        
        try:
            result = await self._call_mcp_method("tools/call", {
                "name": tool_name,
                "arguments": arguments
            })
            
            return result or {"error": "工具调用返回空结果"}
            
        except Exception as e:
            logger.error(f"调用工具 {tool_name} 失败: {e}")
            return {"error": str(e)}
    
    def get_tools_for_openai(self) -> List[Dict[str, Any]]:
        """获取OpenAI格式的工具列表"""
        return [tool.to_openai_format() for tool in self.tools]
    
    def get_tool_by_name(self, name: str) -> Optional[MCPTool]:
        """根据名称获取工具"""
        for tool in self.tools:
            if tool.name == name:
                return tool
        return None

# 预定义的MCP工具信息（作为备用）
PREDEFINED_TOOLS = [
    {
        "name": "get_available_projects",
        "description": "获取可用项目列表",
        "inputSchema": {
            "type": "object",
            "properties": {},
            "required": []
        }
    },
    {
        "name": "switch_project",
        "description": "切换当前活动项目",
        "inputSchema": {
            "type": "object",
            "properties": {
                "project_key": {
                    "type": "string",
                    "description": "项目键值 (F2, F3)"
                }
            },
            "required": ["project_key"]
        }
    },
    {
        "name": "get_signal_exact",
        "description": "精确查询CAN信号信息",
        "inputSchema": {
            "type": "object",
            "properties": {
                "signal_name": {
                    "type": "string",
                    "description": "信号名称（精确匹配）"
                },
                "project_key": {
                    "type": "string",
                    "description": "项目键值 (F2, F3)，如果不指定则使用当前项目"
                }
            },
            "required": ["signal_name"]
        }
    },
    {
        "name": "search_signals_fuzzy",
        "description": "模糊查询CAN信号信息（支持中文关键词搜索）",
        "inputSchema": {
            "type": "object",
            "properties": {
                "keyword": {
                    "type": "string",
                    "description": "搜索关键词（支持部分匹配，可搜索信号名称、注释、值描述）"
                },
                "case_sensitive": {
                    "type": "boolean",
                    "description": "是否区分大小写，默认False"
                },
                "max_results": {
                    "type": "integer",
                    "description": "最大返回结果数量，默认50"
                },
                "project_key": {
                    "type": "string",
                    "description": "项目键值 (F2, F3)，如果不指定则使用当前项目"
                }
            },
            "required": ["keyword"]
        }
    },
    {
        "name": "get_message_info",
        "description": "查询CAN消息信息",
        "inputSchema": {
            "type": "object",
            "properties": {
                "message_name": {
                    "type": "string",
                    "description": "消息名称"
                },
                "project_key": {
                    "type": "string",
                    "description": "项目键值 (F2, F3)，如果不指定则使用当前项目"
                }
            },
            "required": ["message_name"]
        }
    },
    {
        "name": "search_messages_fuzzy",
        "description": "模糊查询CAN消息信息",
        "inputSchema": {
            "type": "object",
            "properties": {
                "keyword": {
                    "type": "string",
                    "description": "搜索关键词"
                },
                "case_sensitive": {
                    "type": "boolean",
                    "description": "是否区分大小写，默认False"
                },
                "max_results": {
                    "type": "integer",
                    "description": "最大返回结果数量，默认50"
                },
                "project_key": {
                    "type": "string",
                    "description": "项目键值 (F2, F3)，如果不指定则使用当前项目"
                }
            },
            "required": ["keyword"]
        }
    },
    {
        "name": "get_signals_by_message",
        "description": "根据消息名称获取该消息下的所有信号",
        "inputSchema": {
            "type": "object",
            "properties": {
                "message_name": {
                    "type": "string",
                    "description": "消息名称"
                },
                "project_key": {
                    "type": "string",
                    "description": "项目键值 (F2, F3)，如果不指定则使用当前项目"
                }
            },
            "required": ["message_name"]
        }
    },
    {
        "name": "get_project_statistics",
        "description": "获取项目统计信息",
        "inputSchema": {
            "type": "object",
            "properties": {
                "project_key": {
                    "type": "string",
                    "description": "项目键值 (F2, F3)，如果不指定则返回所有项目统计信息"
                }
            },
            "required": []
        }
    }
]

def create_mcp_client_with_fallback(server_url: str) -> MCPClient:
    """创建MCP客户端，如果连接失败则使用预定义工具"""
    client = MCPClient(server_url)
    
    # 添加预定义工具作为备用
    for tool_data in PREDEFINED_TOOLS:
        tool = MCPTool(
            name=tool_data["name"],
            description=tool_data["description"],
            input_schema=tool_data["inputSchema"]
        )
        client.tools.append(tool)
    
    return client
