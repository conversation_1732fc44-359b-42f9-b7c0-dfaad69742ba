# MCP服务器能力展示系统

这是一个展示MCP (Model Context Protocol) 服务器能力的Web应用，集成了DeepSeek AI和远程MCP工具，提供智能的CAN信号查询服务。

## 功能特性

- 🤖 **智能对话**: 基于DeepSeek AI的自然语言交互
- 🔧 **MCP工具集成**: 连接远程MCP服务器，调用CAN信号查询工具
- 🎨 **精美界面**: 使用Tailwind CSS和Font Awesome的现代化UI设计
- ⚡ **实时交互**: 流式响应，实时展示AI与MCP工具的交互过程
- 📊 **工具可视化**: 清晰展示工具调用过程和结果

## 技术栈

### 前端
- HTML5
- Tailwind CSS
- Font Awesome
- 原生JavaScript (ES6+)

### 后端
- Python 3.8+
- FastAPI
- LangGraph (AI Agent框架)
- httpx (HTTP客户端)
- uvicorn (ASGI服务器)

### AI服务
- DeepSeek API
- MCP (Model Context Protocol)

## 安装和运行

### 1. 环境准备

确保已安装Python 3.8或更高版本。

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 配置环境变量

项目根目录已包含`.env`文件，包含以下配置：

```env
DEEPSEEK_API_KEY=***********************************
DEEPSEEK_API_BASE=https://api.deepseek.com
MCP_SERVER_URL=http://**************:33398/sse/
PORT=8000
```

### 4. 启动应用

```bash
python run.py
```

或者直接使用uvicorn：

```bash
uvicorn app:app --host 0.0.0.0 --port 8000 --reload
```

### 5. 访问应用

打开浏览器访问: http://localhost:8000

## 使用指南

### 可用的MCP工具

系统集成了以下CAN信号查询工具：

1. **get_available_projects** - 获取可用项目列表
2. **switch_project** - 切换当前活动项目
3. **get_signal_exact** - 精确查询CAN信号信息
4. **search_signals_fuzzy** - 模糊查询CAN信号信息
5. **get_message_info** - 查询CAN消息信息
6. **search_messages_fuzzy** - 模糊查询CAN消息信息
7. **get_signals_by_message** - 根据消息名称获取信号
8. **get_project_statistics** - 获取项目统计信息

### 示例查询

- "查看可用的项目列表"
- "搜索包含'速度'的CAN信号"
- "获取项目统计信息"
- "切换到F2项目"
- "查询ESP_02消息的信号"

### 界面功能

- **对话区域**: 显示与AI的对话历史
- **工具调用展示**: 实时显示MCP工具的调用过程和结果
- **示例问题**: 点击侧边栏的示例问题快速开始
- **清空聊天**: 点击垃圾桶图标清空对话历史

## 项目结构

```
├── app.py                 # 主应用文件
├── run.py                 # 启动脚本
├── mcp_tools.py          # MCP工具集成模块
├── langgraph_agent.py    # LangGraph Agent实现
├── requirements.txt      # Python依赖
├── .env                  # 环境变量配置
├── templates/
│   └── index.html        # 主页模板
├── static/
│   ├── app.js           # 前端JavaScript
│   └── style.css        # 自定义样式
└── README.md            # 项目说明
```

## API接口

### POST /api/chat
发送聊天消息，返回流式响应。

**请求体:**
```json
{
  "message": "用户消息内容"
}
```

**响应:** Server-Sent Events (SSE) 流

### POST /api/clear
清空聊天历史。

### GET /api/status
获取系统状态信息。

## 开发说明

### 扩展MCP工具

要添加新的MCP工具，请在`mcp_tools.py`的`PREDEFINED_TOOLS`列表中添加工具定义。

### 自定义AI提示

可以在`langgraph_agent.py`的`MCPAgent`类中修改`system_prompt`来调整AI的行为。

### 界面定制

- 修改`templates/index.html`来调整页面结构
- 修改`static/style.css`来自定义样式
- 修改`static/app.js`来添加前端功能

## 故障排除

1. **连接MCP服务器失败**: 检查MCP_SERVER_URL是否正确，网络是否可达
2. **DeepSeek API调用失败**: 检查API密钥是否有效，是否有足够的配额
3. **工具调用失败**: 检查MCP服务器是否正常运行，工具参数是否正确

## 许可证

本项目仅用于演示目的。
